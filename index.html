<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ساعة مواقيت الصلاة للمساجد</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1e3c72">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
</head>
<body>
    <!-- قائمة الإعدادات -->
    <div class="settings-container">
        <button class="settings-btn" onclick="toggleSettings()">⚙️ إعدادات</button>
        <div class="settings-dropdown" id="settingsDropdown">
            
            <!-- البحث العالمي -->
            <div class="settings-section">
                <h3>🌍 البحث العالمي</h3>
                <div class="search-container">
                    <input type="text" id="globalSearch" placeholder="ابحث عن أي دولة أو مدينة في العالم..." 
                           onkeyup="searchGlobal()" autocomplete="off">
                    <div class="search-results" id="searchResults"></div>
                </div>
                <button class="add-location-btn" onclick="showAddLocationForm()">➕ إضافة موقع جديد</button>
            </div>

            <!-- إضافة موقع جديد -->
            <div class="settings-section add-location-form" id="addLocationForm" style="display: none;">
                <h3>📍 إضافة موقع جديد</h3>
                <input type="text" id="newLocationName" placeholder="اسم المدينة">
                <input type="text" id="newLocationCountry" placeholder="اسم الدولة">
                <input type="number" id="newLocationLat" placeholder="خط العرض" step="any">
                <input type="number" id="newLocationLng" placeholder="خط الطول" step="any">
                <div class="form-buttons">
                    <button onclick="addNewLocation()">إضافة</button>
                    <button onclick="hideAddLocationForm()">إلغاء</button>
                </div>
            </div>

            <!-- الدول العربية والإسلامية -->
            <div class="settings-section">
                <h3>🕌 الدول العربية والإسلامية</h3>
                <select id="countrySelect" onchange="updateCities()">
                    <option value="">اختر الدولة</option>
                    <optgroup label="الدول العربية">
                        <option value="SA">🇸🇦 السعودية</option>
                        <option value="EG">🇪🇬 مصر</option>
                        <option value="AE">🇦🇪 الإمارات العربية المتحدة</option>
                        <option value="JO">🇯🇴 الأردن</option>
                        <option value="LB">🇱🇧 لبنان</option>
                        <option value="SY">🇸🇾 سوريا</option>
                        <option value="IQ">🇮🇶 العراق</option>
                        <option value="KW">🇰🇼 الكويت</option>
                        <option value="QA">🇶🇦 قطر</option>
                        <option value="BH">🇧🇭 البحرين</option>
                        <option value="OM">🇴🇲 عُمان</option>
                        <option value="YE">🇾🇪 اليمن</option>
                        <option value="MA">🇲🇦 المغرب</option>
                        <option value="DZ">🇩🇿 الجزائر</option>
                        <option value="TN">🇹🇳 تونس</option>
                        <option value="LY">🇱🇾 ليبيا</option>
                        <option value="SD">🇸🇩 السودان</option>
                        <option value="SO">🇸🇴 الصومال</option>
                        <option value="DJ">🇩🇯 جيبوتي</option>
                        <option value="KM">🇰🇲 جزر القمر</option>
                        <option value="MR">🇲🇷 موريتانيا</option>
                        <option value="PS">🇵🇸 فلسطين</option>
                    </optgroup>
                    <optgroup label="الدول الإسلامية">
                        <option value="TR">🇹🇷 تركيا</option>
                        <option value="IR">🇮🇷 إيران</option>
                        <option value="PK">🇵🇰 باكستان</option>
                        <option value="AF">🇦🇫 أفغانستان</option>
                        <option value="BD">🇧🇩 بنغلاديش</option>
                        <option value="ID">🇮🇩 إندونيسيا</option>
                        <option value="MY">🇲🇾 ماليزيا</option>
                        <option value="BN">🇧🇳 بروناي</option>
                        <option value="MV">🇲🇻 المالديف</option>
                        <option value="UZ">🇺🇿 أوزبكستان</option>
                        <option value="KZ">🇰🇿 كازاخستان</option>
                        <option value="KG">🇰🇬 قيرغيزستان</option>
                        <option value="TJ">🇹🇯 طاجيكستان</option>
                        <option value="TM">🇹🇲 تركمانستان</option>
                        <option value="AZ">🇦🇿 أذربيجان</option>
                        <option value="AL">🇦🇱 ألبانيا</option>
                        <option value="BA">🇧🇦 البوسنة والهرسك</option>
                        <option value="XK">🇽🇰 كوسوفو</option>
                    </optgroup>
                    <optgroup label="دول أفريقية إسلامية">
                        <option value="NG">🇳🇬 نيجيريا</option>
                        <option value="SN">🇸🇳 السنغال</option>
                        <option value="ML">🇲🇱 مالي</option>
                        <option value="BF">🇧🇫 بوركينا فاسو</option>
                        <option value="NE">🇳🇪 النيجر</option>
                        <option value="TD">🇹🇩 تشاد</option>
                        <option value="GM">🇬🇲 غامبيا</option>
                        <option value="GN">🇬🇳 غينيا</option>
                        <option value="SL">🇸🇱 سيراليون</option>
                        <option value="CI">🇨🇮 ساحل العاج</option>
                        <option value="GH">🇬🇭 غانا</option>
                        <option value="TG">🇹🇬 توغو</option>
                        <option value="BJ">🇧🇯 بنين</option>
                        <option value="CM">🇨🇲 الكاميرون</option>
                        <option value="CF">🇨🇫 جمهورية أفريقيا الوسطى</option>
                        <option value="UG">🇺🇬 أوغندا</option>
                        <option value="TZ">🇹🇿 تنزانيا</option>
                        <option value="KE">🇰🇪 كينيا</option>
                        <option value="ET">🇪🇹 إثيوبيا</option>
                        <option value="ER">🇪🇷 إريتريا</option>
                    </optgroup>
                    <optgroup label="دول أوروبية">
                        <option value="GB">🇬🇧 بريطانيا</option>
                        <option value="FR">🇫🇷 فرنسا</option>
                        <option value="DE">🇩🇪 ألمانيا</option>
                        <option value="IT">🇮🇹 إيطاليا</option>
                        <option value="ES">🇪🇸 إسبانيا</option>
                        <option value="NL">🇳🇱 هولندا</option>
                        <option value="BE">🇧🇪 بلجيكا</option>
                        <option value="SE">🇸🇪 السويد</option>
                        <option value="NO">🇳🇴 النرويج</option>
                        <option value="DK">🇩🇰 الدنمارك</option>
                        <option value="AT">🇦🇹 النمسا</option>
                        <option value="CH">🇨🇭 سويسرا</option>
                        <option value="RU">🇷🇺 روسيا</option>
                    </optgroup>
                    <optgroup label="دول أمريكية">
                        <option value="US">🇺🇸 الولايات المتحدة</option>
                        <option value="CA">🇨🇦 كندا</option>
                        <option value="MX">🇲🇽 المكسيك</option>
                        <option value="BR">🇧🇷 البرازيل</option>
                        <option value="AR">🇦🇷 الأرجنتين</option>
                        <option value="CL">🇨🇱 تشيلي</option>
                        <option value="PE">🇵🇪 بيرو</option>
                        <option value="CO">🇨🇴 كولومبيا</option>
                        <option value="VE">🇻🇪 فنزويلا</option>
                    </optgroup>
                    <optgroup label="دول آسيوية">
                        <option value="IN">🇮🇳 الهند</option>
                        <option value="CN">🇨🇳 الصين</option>
                        <option value="JP">🇯🇵 اليابان</option>
                        <option value="KR">🇰🇷 كوريا الجنوبية</option>
                        <option value="TH">🇹🇭 تايلاند</option>
                        <option value="VN">🇻🇳 فيتنام</option>
                        <option value="PH">🇵🇭 الفلبين</option>
                        <option value="SG">🇸🇬 سنغافورة</option>
                        <option value="LK">🇱🇰 سريلانكا</option>
                        <option value="MM">🇲🇲 ميانمار</option>
                        <option value="KH">🇰🇭 كمبوديا</option>
                        <option value="LA">🇱🇦 لاوس</option>
                        <option value="MN">🇲🇳 منغوليا</option>
                        <option value="NP">🇳🇵 نيبال</option>
                        <option value="BT">🇧🇹 بوتان</option>
                    </optgroup>
                    <optgroup label="دول أوقيانوسية">
                        <option value="AU">🇦🇺 أستراليا</option>
                        <option value="NZ">🇳🇿 نيوزيلندا</option>
                        <option value="FJ">🇫🇯 فيجي</option>
                        <option value="PG">🇵🇬 بابوا غينيا الجديدة</option>
                    </optgroup>
                </select>
                <select id="citySelect" onchange="updateLocation()">
                    <option value="">اختر المدينة</option>
                </select>
            </div>

            <!-- الأماكن المفضلة -->
            <div class="settings-section">
                <h3>⭐ الأماكن المفضلة</h3>
                <div id="favoriteLocations" class="favorite-locations"></div>
            </div>

            <!-- إعدادات الوقت -->
            <div class="settings-section">
                <h3>⏰ إعدادات الوقت</h3>
                <label>
                    <input type="radio" name="timeFormat" value="12" checked onchange="updateTimeFormat()"> 12 ساعة
                </label>
                <label>
                    <input type="radio" name="timeFormat" value="24" onchange="updateTimeFormat()"> 24 ساعة
                </label>
                <label>
                    <input type="checkbox" id="dstToggle" onchange="updateDST()"> التوقيت الصيفي التلقائي
                </label>
                <div class="timezone-info">
                    <span>المنطقة الزمنية: </span>
                    <span id="timezoneDisplay">UTC+0</span>
                </div>
            </div>

            <!-- إعدادات الخلفية -->
            <div class="settings-section">
                <h3>🎨 الخلفية والألوان</h3>
                <select id="backgroundSelect" onchange="changeBackground()">
                    <option value="backgrounds/background1.jpg">خلفية 1 - مسجد الحرام</option>
                    <option value="backgrounds/background2.jpg">خلفية 2 - المسجد النبوي</option>
                    <option value="backgrounds/background3.jpg">خلفية 3 - مسجد الأقصى</option>
                    <option value="backgrounds/background4.jpg">خلفية 4 - مسجد الشيخ زايد</option>
                    <option value="backgrounds/background5.jpg">خلفية 5 - مسجد السلطان أحمد</option>
                    <option value="backgrounds/background6.jpg">خلفية 6 - مسجد قبة الصخرة</option>
                    <option value="backgrounds/background7.jpg">خلفية 7 - مسجد الحسن الثاني</option>
                    <option value="backgrounds/background8.jpg">خلفية 8 - مسجد فيصل</option>
                </select>
                <div class="color-themes">
                    <label>اختر اللون الأساسي:</label>
                    <div class="color-options">
                        <button class="color-btn" data-color="#4CAF50" style="background: #4CAF50"></button>
                        <button class="color-btn" data-color="#2196F3" style="background: #2196F3"></button>
                        <button class="color-btn" data-color="#FF9800" style="background: #FF9800"></button>
                        <button class="color-btn" data-color="#9C27B0" style="background: #9C27B0"></button>
                        <button class="color-btn" data-color="#F44336" style="background: #F44336"></button>
                        <button class="color-btn" data-color="#795548" style="background: #795548"></button>
                    </div>
                </div>
            </div>

            <!-- إعدادات الأذان -->
            <div class="settings-section">
                <h3>🔊 إعدادات الأذان</h3>
                <label>
                    <input type="checkbox" id="azanToggle" checked onchange="toggleAzan()"> تشغيل الأذان
                </label>
                <select id="azanSelect" onchange="changeAzanSound()">
                    <option value="audio/audio_azan.mp3">أذان الحرم المكي</option>
                    <option value="audio/short_azan.mp3">أذان قصير</option>
                    <option value="audio/audio_wbeeep.mp3">تنبيه بسيط</option>
                    <option value="audio/audio_wtit.wav">صوت تنبيه</option>
                </select>
                <div class="volume-control">
                    <label>مستوى الصوت:</label>
                    <input type="range" id="volumeSlider" min="0" max="100" value="50" onchange="updateVolume()">
                    <span id="volumeValue">50%</span>
                </div>
                <label>
                    <input type="checkbox" id="notificationToggle" checked> إشعارات قبل الأذان
                </label>
                <select id="notificationTime">
                    <option value="5">5 دقائق قبل الأذان</option>
                    <option value="10">10 دقائق قبل الأذان</option>
                    <option value="15">15 دقيقة قبل الأذان</option>
                    <option value="30">30 دقيقة قبل الأذان</option>
                </select>
            </div>

            <!-- إعدادات النص المتحرك -->
            <div class="settings-section">
                <h3>📜 النص المتحرك</h3>
                <select id="scrollingTextSelect" onchange="changeScrollingText()">
                    <option value="default">النص الافتراضي</option>
                    <option value="quran">آيات قرآنية</option>
                    <option value="hadith">أحاديث نبوية</option>
                    <option value="dua">أدعية</option>
                    <option value="custom">نص مخصص</option>
                </select>
                <textarea id="customText" placeholder="اكتب النص المخصص هنا..." style="display: none;"></textarea>
                <div class="text-speed">
                    <label>سرعة النص:</label>
                    <input type="range" id="textSpeed" min="10" max="50" value="30" onchange="updateTextSpeed()">
                </div>
            </div>
        </div>
    </div>
